/**
 * NEURON IIoT System for Industry 4.0
 * Copyright (C) 2020-2024 EMQ Technologies Co., Ltd All rights reserved.
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 3 of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this program; if not, write to the Free Software Foundation,
 * Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.
 **/

#include "otel/otel_manager.h"
#include "utils/asprintf.h"
#include "utils/time.h"
#include "acme_lora.h"
#include <stdlib.h>

#include <neuron.h>
#include "acme_node.h"
#include "errcodes.h"
#include "business.h"
#include "neuron/msg.h"

static neu_plugin_t *lora_driver_open(void);

static int lora_driver_close(neu_plugin_t *plugin);
static int lora_driver_init(neu_plugin_t *plugin, bool load);
static int lora_driver_uninit(neu_plugin_t *plugin);
static int lora_driver_start(neu_plugin_t *plugin);
static int lora_driver_stop(neu_plugin_t *plugin);
static int lora_driver_config(neu_plugin_t *plugin, const char *config);
static int lora_driver_request(neu_plugin_t *plugin, neu_reqresp_head_t *head,
                          void *data);
static int lora_driver_create(neu_plugin_t *plugin, void *data);

static int lora_driver_validate_tag(neu_plugin_t *plugin, neu_datatag_t *tag);
static int lora_driver_group_timer(neu_plugin_t *plugin, neu_plugin_group_t *group);
static int lora_driver_write(neu_plugin_t *plugin, void *req, neu_datatag_t *tag,
                        neu_value_u value);
static int lora_driver_write_tags(neu_plugin_t *plugin, void *req, UT_array *tags);

static const neu_plugin_intf_funs_t acme_lora_plugin_intf_funs = {
    .open    = lora_driver_open,
    .close   = lora_driver_close,
    .init    = lora_driver_init,
    .uninit  = lora_driver_uninit,
    .start   = lora_driver_start,
    .stop    = lora_driver_stop,
    .setting = lora_driver_config,
    .request = lora_driver_request,
    .create  = lora_driver_create,

    .driver.validate_tag  = lora_driver_validate_tag,
    .driver.group_timer   = lora_driver_group_timer,
    .driver.group_sync    = lora_driver_group_timer,
    .driver.write_tag     = lora_driver_write,
    .driver.tag_validator = NULL,
    .driver.write_tags    = lora_driver_write_tags,
    .driver.add_tags      = NULL,
    .driver.load_tags     = NULL,
    .driver.del_tags      = NULL,

    //TODO: 以上是插件驱动对外接口，通过适配器调用，后续可灵活新增接口，用于实现特定业务，只需要在适配器中做好功能对接即可


};

const neu_plugin_module_t neu_plugin_module = {
    .version     = NEURON_PLUGIN_VER_1_0,
    .schema      = "acme-lora",
    .module_name = "ACME_LORA",
    .module_descr = "This plugin is used for ACME Lora driver type devices. Support module point click&acquisition control",
    .module_descr_zh = "该插件用于ACME Lora驱动类型设备。支持模块点位点击&采集控制",
    .intf_funs = &acme_lora_plugin_intf_funs,
    .kind      = NEU_PLUGIN_KIND_SYSTEM,
    .type      = NEU_NA_TYPE_DRIVER,
    .display   = true,
    .single    = false,
};


/*********************************** Lora 设备驱动插件接口实现 *****************************************/

/*
* node 节点创建时，根据具体设备型号，如 FCM 等进行设备默认组和对应点位的创建接口
* 或者其他类型的设备在创建时候需要进行的其他任务，在此接口进行封装并调用
*/
static int lora_driver_create(neu_plugin_t *plugin, void *data)
{
    (void) plugin;
    (void) data;

    neu_err_code_e error = NEU_ERR_SUCCESS;





    return error;
}

static neu_plugin_t *lora_driver_open(void)
{
    neu_plugin_t *plugin = calloc(1, sizeof(neu_plugin_t));

    neu_plugin_common_init(&plugin->common);

    return plugin;
}

static int lora_driver_close(neu_plugin_t *plugin)
{
    free(plugin);

    return 0;
}

static int lora_driver_init(neu_plugin_t *plugin, bool load)
{
    (void) load;



    plog_notice(plugin, "%s init success", plugin->common.name);
    return 0;
}

static int lora_driver_uninit(neu_plugin_t *plugin)
{


    return 0;
}

/*
* Lora 子设备 启动业务
*/
static int lora_driver_start(neu_plugin_t *plugin)
{  
    nlog_debug("lora subdev:%s start....",plugin->common.name);

    node_base_t * dev = plugin->common.adapter_callbacks->get_dev_base(plugin->common.adapter); 
    if(dev == NULL){
        plog_notice(plugin, "%s adapter acme dev baseInfo is NULL !!!!", plugin->common.name);
        return -1;
    }

    nlog_debug("lora subdev start 1....");

    //子设备发送注册命令到网关
    neu_reqresp_head_t header = {
        .type = NEU_REQ_ACME_DEV_REGISTER,
        .ctx  = plugin,
    };

    neu_acme_subDev_reg_t cmd = {
        .mid            = dev->mid,
        .modeType       = dev->modeType,
        .pid            = dev->pid,
        .link           = dev->link,
        .online         = 1,                //默认在线
        .deviceCode     = dev->deviceCode,
        .subType        = dev->subType,
    };

    nlog_debug("lora subdev start 2....");

    strcpy(cmd.node, plugin->common.name);
    strcpy(cmd.version, dev->version);
    strcpy(cmd.eui, dev->eui);


    nlog_debug("send subdev:%s register info.",cmd.node);

    if (0 != neu_plugin_op(plugin, header, &cmd)) {
        plog_error(plugin, "neu_plugin_op(NEU_REQ_ACME_DEV_REGISTER) fail");
        return -1;
    }

    nlog_debug("lora subdev end 3....");

    return 0;
}

static int lora_driver_stop(neu_plugin_t *plugin)
{
    return 0;
}

static int lora_driver_config(neu_plugin_t *plugin, const char *config)
{
    nlog_debug("lora dev config.");
    return 0;
}

static int lora_driver_request(neu_plugin_t *plugin, neu_reqresp_head_t *head,
                          void *data)
{
    (void) plugin;
    (void) head;
    (void) data;

    neu_err_code_e error = NEU_ERR_SUCCESS;
    node_base_t * dev = plugin->common.adapter_callbacks->get_dev_base(plugin->common.adapter); 
    if(dev == NULL){
        plog_notice(plugin, "%s adapter acme dev baseInfo is NULL !!!!", plugin->common.name);
        return -1;
    }




    switch (head->type) {
    case NEU_RESP_ERROR:{
        neu_resp_error_t *errInfo = (neu_resp_error_t *)data;
        plog_notice(plugin, "manager notify error code %d ",errInfo->error);
        break;    
    }
    case NEU_REQ_ACME_NOTIFY_DEV_JOIN:{
        //TODO: 子设备已配对 成功
        neu_acme_notify_dev_join_t *devInfo = (neu_acme_notify_dev_join_t *)data;
        if(devInfo == NULL){
            nlog_debug("parameter error !!!");            
            return -1;
        }
        nlog_debug("dev:%s recv gw pair info.  eui:%s || node:%s ",plugin->common.name,devInfo->eui,devInfo->node);
        
        //配对成功是否需要做通知，在此后面添加功能即可
        //...

        break;
    }
    case NEU_REQ_NODE_CTL:{
        neu_req_node_ctl_t *cmd   = (neu_req_node_ctl_t *)data;
        switch (cmd->ctl)
        {
            case NEU_ADAPTER_CTL_SEARCH_DEV: {       //搜索设备命令
                neu_req_node_ctl_t *cmd   = (neu_req_node_ctl_t *)data;
                business_lora_dev_search(plugin, dev, head);          
                break;
            }
            case NEU_ADAPTER_CTL_DEV_CHN:{           //设备通道直接控制
                neu_req_node_ctl_t *cmd   = (neu_req_node_ctl_t *)data;
                business_lora_dev_ctrl(plugin, dev, cmd->params,head);
                break;
            }  
            default:
                error = NEU_ERR_NODE_EXIST;
                break;
        }
        break;        
    }
    case NEU_REQ_ADD_GROUP:{
        //添加设备组，需要针对特定产品区分是否是产品默认组，然后选择性生成默认点位
        neu_req_add_group_t *group = (neu_req_add_group_t *)data;
        business_lora_dev_add_group(plugin, dev,group);
        break;
    }
    case NEU_REQ_ACME_DEV_DATA_POP:{
        neu_acme_dev_data_pop_t * pInfo = (neu_acme_dev_data_pop_t *)data;
        nlog_debug("node:%s recv gw driver pop data[%02x] eui:%s , dataLen=%d data-->",pInfo->node,pInfo->rsp,pInfo->eui,pInfo->len);
        if(pInfo->data){
            hex2str_printf(pInfo->data,pInfo->len-1);
        }

        //TODO:解析各种类型设备的业务数据
        //...
        




        free(pInfo->data);
        break;
    }
    default:
        break;
    }
    return error;
}

static int lora_driver_validate_tag(neu_plugin_t *plugin, neu_datatag_t *tag)
{
    nlog_debug("***node:%s tag:%s validate ok.***",plugin->common.name,tag->name);
    return 0;
}

static int lora_driver_group_timer(neu_plugin_t *plugin, neu_plugin_group_t *group)
{
    //nlog_debug("***node:%s group:%s timer process .***",plugin->common.name,group->group_name);
    return 0;
}

static int lora_driver_write(neu_plugin_t *plugin, void *req, neu_datatag_t *tag,
                        neu_value_u value)
{
    nlog_debug("***node:%s tag:%s lora write value:%d  .***",plugin->common.name,tag->name,value);

    node_base_t * dev = plugin->common.adapter_callbacks->get_dev_base(plugin->common.adapter); 
    if(dev == NULL){
        plog_notice(plugin, "%s adapter acme dev baseInfo is NULL !!!!", plugin->common.name);
        return -1;
    }

    business_lora_dev_write_tag(plugin,dev,tag,value);      //写点位 业务
    return 0;
}

static int lora_driver_write_tags(neu_plugin_t *plugin, void *req, UT_array *tags)
{
    return 0;
}

