/**
 * NEURON IIoT System for Industry 4.0
 * Copyright (C) 2020-2024 EMQ Technologies Co., Ltd All rights reserved.
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 3 of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this program; if not, write to the Free Software Foundation,
 * Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.
 **/

#ifndef NEURON_PLUGIN_ACME_LORA_BUSINESS_H
#define NEURON_PLUGIN_ACME_LORA_BUSINESS_H

#ifdef __cplusplus
extern "C" {
#endif
#include <stdlib.h>
#include <neuron.h>
#include "neuron.h"
#include "acme_node.h"
#include "business_fcm.h"

int hex2str_printf(char *data,int nByte);

int business_lora_dev_search(neu_plugin_t *plugin,node_base_t * dev, neu_reqresp_head_t *head);     //搜索子设备
int business_lora_dev_ctrl(neu_plugin_t *plugin,node_base_t * dev, void * params, neu_reqresp_head_t *head);  //设备直接控制
int business_lora_dev_add_group(neu_plugin_t *plugin, node_base_t * dev ,neu_req_add_group_t *group);
int business_lora_dev_write_tag(neu_plugin_t *plugin, node_base_t * dev, neu_datatag_t *tag,neu_value_u value);     //写点位业务请求

#ifdef __cplusplus
}
#endif

#endif
