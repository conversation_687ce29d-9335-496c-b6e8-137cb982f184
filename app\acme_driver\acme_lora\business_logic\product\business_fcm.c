/**
 * NEURON IIoT System for Industry 4.0
 * Copyright (C) 2020-2024 EMQ Technologies Co., Ltd All rights reserved.
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 3 of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this program; if not, write to the Free Software Foundation,
 * Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.
 **/

/*************************************************************
*  lora FCM 空调面板 设备业务逻辑层
*  主要实现 FCM 类型设备的 Lora 协议转换、业务接口实现（如设备搜索功能）
*************************************************************/
#include <string.h>
#include "otel/otel_manager.h"
#include "utils/asprintf.h"
#include "utils/time.h"
#include "acme_lora.h"
#include <stdlib.h>
#include <neuron.h>
#include "errcodes.h"
#include "business_fcm.h"
#include "util.h"
#include "lora_cmd.h"
#include "adapter/driver/driver_internal.h"

// 静态函数声明
static int fcm_process_io_status_data(neu_plugin_t *plugin, node_base_t * dev, neu_acme_dev_data_pop_t *pInfo);
static int fcm_process_point_value_data(neu_plugin_t *plugin, node_base_t * dev, neu_acme_dev_data_pop_t *pInfo);
static int fcm_update_tag_values(neu_plugin_t *plugin, node_base_t * dev,
                                uint16_t temperature, uint16_t set_temperature, uint8_t mode,
                                uint8_t power_switch, uint8_t high_wind, uint8_t medium_wind,
                                uint8_t low_wind, uint16_t humidity, uint8_t wind_auto_flag);
static int fcm_update_tag_by_address(neu_plugin_t *plugin, neu_adapter_driver_t *driver,
                                     const char *address, neu_type_e tag_type, neu_value_u value);
static int fcm_update_tag_value_direct(neu_plugin_t *plugin, neu_adapter_driver_t *driver,
                                       const char *group_name, const char *tag_name,
                                       neu_type_e tag_type, neu_value_u value);
static neu_value_u fcm_create_float_value(float float_value);
static neu_value_u fcm_create_int_value(int32_t int_value);

/*
* FCM 设备通道直接控制
*/
int fcm_dev_ctrl(neu_plugin_t *plugin, node_base_t * dev ,void *params)
{
    int ret = 0;
    void *                   json_obj = NULL;
    if(plugin == NULL || dev == NULL || params == NULL){
        nlog_debug("parameter error !!!");
        return -1;
    }


    json_obj = neu_json_decode_new(params);
    if (NULL == json_obj) {
        nlog_debug("params json parse error !!!");
        return -1;
    }

    void * json_params = json_object_get(json_obj, "params");
    if(json_params == NULL){
        nlog_debug("params parse error !!!");
        json_decref(json_obj);
        return -1;
    }

    void * json_actions = json_object_get(json_params, "actions");
    if(json_params == NULL){
        nlog_debug("no `actions` array field");
        json_decref(json_obj);
        return -1;
    }

    int num = json_array_size(json_actions);
    nlog_debug("params actions num=%d ",num);

    //遍历控制动作json 数组
    UT_array *actions = NULL;
    UT_icd    icd = { sizeof(neu_acme_dev_action_t), NULL, NULL, NULL };
    utarray_new(actions, &icd); 

    for (int i = 0; i < num; i++) {
        neu_json_elem_t action_elems[] = {
            {
                .name = "type",
                .t    = NEU_JSON_STR,
            },
            {
                .name = "chn",
                .t    = NEU_JSON_INT,
            },
            {
                .name = "value",
                .t    = NEU_JSON_DOUBLE,
            }
        };

        ret = neu_json_decode_by_json(json_array_get(json_actions, i),NEU_JSON_ELEM_SIZE(action_elems), action_elems);
        char * type          = action_elems[0].v.val_str;
        int chn              = action_elems[1].v.val_int;
        double_t value       = action_elems[2].v.val_double;

        nlog_debug("***action[%d] type:%s || chn:%d || info:%f ", i, type, chn, value);

        //TODO: 对通道设置命令，在此进行结构体封装，通过消息发送到 网关进行Lora 组包发送

        neu_acme_dev_action_t action = { 0 };
        strcpy(action.type,type);
        action.chn      = chn;
        Float_to_Byte(value, action.value);
        action.trigger  = 2;        //触发源:  2 -- web 配置
        utarray_push_back(actions, &action);
    }

    neu_reqresp_head_t         header                       = { 0 };
    header.ctx             = plugin;
    header.type = NEU_REQ_ACME_DEV_CTRL_PUSH;

    nlog_debug("fcm_dev_ctrl send msg: NEU_REQ_ACME_DEV_CTRL_PUSH");
    neu_acme_dev_ctrl_t info = {0};
    strcpy(info.node,plugin->common.name);
    strcpy(info.eui,dev->eui);
    info.actions = actions;

    if (0 != neu_plugin_op(plugin, header, &info)) {
        plog_error(plugin, "neu_plugin_op(NEU_REQ_ACME_DEV_CTRL_PUSH) fail");
        ret = -1;
        goto  decode_fail;
    }
    
decode_fail:
    json_decref(json_obj);


    return ret;
}

/*
* FCM 设备类型 写点位
*/
int fcm_dev_write_tag(neu_plugin_t *plugin, node_base_t * dev,neu_datatag_t *tag,neu_value_u value)
{
    int ret = 0;
    neu_acme_dev_action_t action = { 0 };
    UT_array *actions = NULL;
    UT_icd    icd = { sizeof(neu_acme_dev_action_t), NULL, NULL, NULL };
    //写点位 需要区分点位param 类型，如果是系统默认点位，按照上面通道直接控制方式进行组装 NEU_REQ_ACME_DEV_CTRL_PUSH 命令下发到网关即可
    if(tag->params != NULL ){
        neu_json_elem_t ele_sCom    = { .name = "sCom", .t = NEU_JSON_INT };
        neu_json_elem_t ele_sPro    = { .name = "sPro", .t = NEU_JSON_INT };
        ret = neu_parse_param(tag->params, NULL, 2, &ele_sCom, &ele_sPro);
        if (ret != 0) {
            nlog_debug("tag->params parse error !!!");
            return -1;
        }
        int sCom = ele_sCom.v.val_int;
        int sPro = ele_sPro.v.val_int;

        nlog_debug("tag:%s sCom=%d sPro=%d ",tag->name,sCom,sPro);
        if((sCom == 30) && (sPro == 39)){       //FCM 
            int addr = atoi(tag->address);
            
            action.trigger  = 2;        //触发源:  2 -- web 配置
            switch (addr)
            {
                case ACME_FCM_ADDR_ONOFF:{     //开关
                    strcpy(action.type,ACME_FCM_TAG_TYPE_BO);
                    action.chn      = ACME_FCM_CHN_ONOFF;
                    Float_to_Byte(value.u32, action.value);                    
                    break;
                }
                case ACME_FCM_ADDR_STEMP:{     //设置温度
                    strcpy(action.type,ACME_FCM_TAG_TYPE_AO);
                    action.chn      = ACME_FCM_CHN_SET_TEMP;
                    Float_to_Byte(value.f32, action.value);    
                    break;
                }
                case ACME_FCM_ADDR_SMODE:{     //设置模式
                    strcpy(action.type,ACME_FCM_TAG_TYPE_AO);
                    action.chn      = ACME_FCM_CHN_MODE;
                    Float_to_Byte(value.u32, action.value);    
                    break;
                }
                case ACME_FCM_ADDR_WSPED:{     //设置风速
                    strcpy(action.type,ACME_FCM_TAG_TYPE_BO);
                    if(value.u32 == 1){
                        action.chn      = ACME_FCM_CHN_FAN_HIGH;            //高速风
                    }else if(value.u32 == 2){
                        action.chn      = ACME_FCM_CHN_FAN_MEDIUM;            //中速风    
                    }else if(value.u32 == 3){
                        action.chn      = ACME_FCM_CHN_FAN_LOW;            //低速风    
                    }else if(value.u32 == 4){
                        action.chn      = ACME_FCM_CHN_FAN_AUTO;            //自动风    
                    }else{
                        action.chn      = ACME_FCM_CHN_FAN_AUTO;            //自动风   
                    }                    
                    Float_to_Byte(1, action.value);    //置位
                    break;
                }
                case ACME_FCM_ADDR_RTEMP:
                case ACME_FCM_ADDR_ERROC:
                case ACME_FCM_ADDR_HUMID:{     
                    nlog_debug("tag:%s read only !",tag->name);
                    return -1;
                }
            
                default:
                  return 0;
            }

        }else{
            nlog_debug("tag:%s  sCom and sPro not support ! ",tag->name);
            return -1;
        }
    }else{
        nlog_debug("tag->params is NULL !!!");
        return -1;
    }

    utarray_new(actions, &icd); 
    utarray_push_back(actions, &action);

    neu_reqresp_head_t         header                       = { 0 };
    header.ctx             = plugin;
    header.type = NEU_REQ_ACME_DEV_CTRL_PUSH;

    nlog_debug("fcm_dev_ctrl send msg: NEU_REQ_ACME_DEV_CTRL_PUSH");
    neu_acme_dev_ctrl_t info = {0};
    strcpy(info.node,plugin->common.name);
    strcpy(info.eui,dev->eui);
    info.actions = actions;

    if (0 != neu_plugin_op(plugin, header, &info)) {
        plog_error(plugin, "neu_plugin_op(NEU_REQ_ACME_DEV_CTRL_PUSH) fail");
        ret = -1;        
    }

    return ret;
}

/*
* FCM 类型设备搜索子设备命令
*/
void * fcm_dev_search(char *node_name)
{
    if(node_name == NULL){
        nlog_debug("parameter error !!!");
        return NULL;
    }

    UT_array *devs = NULL;
    UT_icd    icd = { sizeof(neu_resp_acme_dev_search_t), NULL, NULL, NULL };

    utarray_new(devs, &icd); 
    neu_resp_acme_dev_search_t info = { 0 };

    strcpy(info.node,node_name);
    sprintf(info.name,"%s_default_group",node_name);
    info.interval   = ACME_FCM_DEFAULT_INTERVAL;
    info.addr       = 0;
    info.type       = NEU_GROUP_TYPE_ACM;
    strcpy(info.description,"FCM 空调面板默认设备组");

    utarray_push_back(devs, &info);
        
    return devs;
}

/*
* 创建 FCM  默认 点位
*/
static int generate_default_fcm_dev_tag_by_name(char * name, neu_datatag_t *tag)
{
    int ret = 0;
    if(name == NULL || tag == NULL){
        nlog_debug("parameter error !!!");
        return -1;
    }
    char * params = NULL;
    void *object = neu_json_encode_new();
    neu_json_elem_t elems[] = { 
    {
        .name         = "sCom",
        .t            = NEU_JSON_INT,
        .v.val_int    = 30,                 //30 -- lora 协议
    },
    {
        .name         = "sPro",
        .t            = NEU_JSON_INT,
        .v.val_int      = 39,           //E_PRO_ACME_AIRLINK
    }};
    void *par = neu_json_encode_new();
    ret = neu_json_encode_field(par, elems,
                                NEU_JSON_ELEM_SIZE(elems));

    neu_json_elem_t params_elems[] = { 
    {
        .name         = "params",
        .t            = NEU_JSON_OBJECT,
        .v.val_object = par,
    }};
    ret = neu_json_encode_field(object, params_elems,
                                NEU_JSON_ELEM_SIZE(params_elems));

    ret = neu_json_encode(object, &params);
    neu_json_decode_free(object);

    nlog_debug("default params :%s",params);
   
    

    if(strcmp(name,"ONOFF") == 0){
        tag->name           = strdup(name);
        tag->description    = strdup("开关");
        tag->type           = NEU_TYPE_INT32;
        tag->address        = strdup("1");
        tag->attribute      =  NEU_ATTRIBUTE_READ | NEU_ATTRIBUTE_WRITE;
        tag->decimal        = 0;
        tag->pLink          = 2;        //网络点
        strcpy(tag->unit,"switch");
        tag->params         = strdup(params);
    }else if(strcmp(name,"STEMP") == 0){
        tag->name           = strdup(name);
        tag->description    = strdup("设置温度");
        tag->type           = NEU_TYPE_FLOAT;
        tag->address        = strdup("2");
        tag->attribute      =  NEU_ATTRIBUTE_READ | NEU_ATTRIBUTE_WRITE;
        tag->decimal        = 0;
        tag->pLink          = 2;        //网络点
        strcpy(tag->unit,"℃");
        tag->params         = strdup(params);        
    }else if(strcmp(name,"SMODE") == 0){
        tag->name           = strdup(name);
        tag->description    = strdup("设置模式");
        tag->type           = NEU_TYPE_INT32;
        tag->address        = strdup("3");
        tag->attribute      =  NEU_ATTRIBUTE_READ | NEU_ATTRIBUTE_WRITE;
        tag->decimal        = 0;
        tag->pLink          = 2;        //网络点
        strcpy(tag->unit,"/h/w/d/c");
        tag->params         = strdup(params); 
    }else if(strcmp(name,"WSPED") == 0){
        tag->name           = strdup(name);
        tag->description    = strdup("设置风速");
        tag->type           = NEU_TYPE_INT32;
        tag->address        = strdup("4");
        tag->attribute      =  NEU_ATTRIBUTE_READ | NEU_ATTRIBUTE_WRITE;
        tag->decimal        = 0;
        tag->pLink          = 2;        //网络点
        strcpy(tag->unit,"/l/m/h/a");
        tag->params         = strdup(params); 
    }else if(strcmp(name,"RTEMP") == 0){
        tag->name           = strdup(name);
        tag->description    = strdup("读取温度");
        tag->type           = NEU_TYPE_INT32;
        tag->address        = strdup("5");
        tag->attribute      =  NEU_ATTRIBUTE_READ;
        tag->decimal        = 0;
        tag->pLink          = 2;        //网络点
        strcpy(tag->unit,"℃");
        tag->params         = strdup(params); 
    }else if(strcmp(name,"ERROC") == 0){
        tag->name           = strdup(name);
        tag->description    = strdup("错误码");
        tag->type           = NEU_TYPE_INT32;
        tag->address        = strdup("6");
        tag->attribute      =  NEU_ATTRIBUTE_READ;
        tag->decimal        = 0;
        tag->pLink          = 2;        //网络点
        strcpy(tag->unit,"!");
        tag->params         = strdup(params); 
    }else if(strcmp(name,"HUMID") == 0){
        tag->name           = strdup(name);
        tag->description    = strdup("湿度");
        tag->type           = NEU_TYPE_INT32;
        tag->address        = strdup("7");
        tag->attribute      =  NEU_ATTRIBUTE_READ;
        tag->decimal        = 0;
        tag->pLink          = 2;        //网络点
        strcpy(tag->unit,"%");
        tag->params         = strdup(params); 
    }else {
        nlog_debug("parameter name:%s not support !!!",name);
        ret =  -1;
    }

    free(params);

    return ret;
}


/*
* 生成 FCM 默认点位
*/
static int generate_default_fcm_dev_tags(node_base_t * dev ,neu_req_add_group_t *group, neu_req_add_tag_t *cmd)
{
    int ret = 0;
    nlog_debug("generate fcm tags start...");

    strcpy(cmd->driver, group->driver);
    strcpy(cmd->group, group->group);
    
    cmd->n_tag = ACME_FCM_DEFUALT_TAGS_NUM;
    cmd->tags  = calloc(cmd->n_tag, sizeof(neu_datatag_t));
    if (NULL == cmd->tags) {
        return NEU_ERR_EINTERNAL;
    }

    int i = 0;
    generate_default_fcm_dev_tag_by_name("ONOFF", &cmd->tags[i++]);
    generate_default_fcm_dev_tag_by_name("STEMP", &cmd->tags[i++]);
    generate_default_fcm_dev_tag_by_name("SMODE", &cmd->tags[i++]);
    generate_default_fcm_dev_tag_by_name("WSPED", &cmd->tags[i++]);
    generate_default_fcm_dev_tag_by_name("RTEMP", &cmd->tags[i++]);
    generate_default_fcm_dev_tag_by_name("ERROC", &cmd->tags[i++]);
    generate_default_fcm_dev_tag_by_name("HUMID", &cmd->tags[i++]);

    nlog_debug("generate fcm tags end...");
    return ret;
}

/*
* fcm 设备 添加组
*/
int fcm_dev_add_group(neu_plugin_t *plugin, node_base_t * dev ,neu_req_add_group_t *group)
{
    int ret = 0;
    if(group->type != NEU_GROUP_TYPE_ACM){      //非产品默认组，不需要处理
        nlog_debug("dev:%s group type:%d not handled",plugin->common.name, group->type);
        return 0;
    }

    //FCM 设备添加默认组时候，需要同时添加默认点位
    //TODO: 此处发送添加默认点位的消息到 Core
    //...
    
    neu_reqresp_head_t header = {
        .type            = NEU_REQ_ADD_TAG,
        .ctx             = plugin,       
    };

    neu_req_add_tag_t cmd = { 0 };
    generate_default_fcm_dev_tags(dev,group,&cmd);

    if (0 != neu_plugin_op(plugin, header, &cmd)) {
        ret = NEU_ERR_IS_BUSY;
        goto error;
    }

    return ret;

error:
    for (int j = 0;  j < cmd.n_tag; ++j) {
        neu_tag_fini(&cmd.tags[j]);
    }
    free(cmd.tags);
    return ret;
}

/*
* FCM 设备消息处理函数
* 处理空调设备上报的数据，解析并更新对应的点位值
*/
int fcm_dev_message_handle(neu_plugin_t *plugin, node_base_t * dev, neu_acme_dev_data_pop_t *pInfo)
{
    int ret = 0;
    if(plugin == NULL || dev == NULL || pInfo == NULL || pInfo->data == NULL){
        nlog_debug("parameter error !!!");
        return -1;
    }

    nlog_debug("FCM device:%s recv message rsp:0x%02x len:%d",
               plugin->common.name, pInfo->rsp, pInfo->len);

    // 根据不同的响应类型处理数据
    switch (pInfo->rsp)
    {
        case LR_RSP_IOM_LP:{  // IO状态上报
            ret = fcm_process_io_status_data(plugin, dev, pInfo);
            break;
        }
        case LR_RSP_PTV_REP:{ // 点值上报
            ret = fcm_process_point_value_data(plugin, dev, pInfo);
            break;
        }
        default:
            nlog_debug("FCM device unsupported message type: 0x%02x", pInfo->rsp);
            ret = -1;
            break;
    }

    return ret;
}

/*
* 处理 FCM 设备 IO 状态数据
* 解析空调设备的状态信息并更新相应的点位
*/
static int fcm_process_io_status_data(neu_plugin_t *plugin, node_base_t * dev, neu_acme_dev_data_pop_t *pInfo)
{
    int ret = 0;
    uint8_t *data = (uint8_t *)pInfo->data;
    int data_len = pInfo->len - 1; // 减去RSP字节

    if(data_len < 20){  // 最少需要20字节的空调数据
        nlog_debug("FCM IO status data length too short: %d", data_len);
        return -1;
    }

    nlog_debug("FCM processing IO status data, length: %d", data_len);

    // 解析空调数据结构
    // 格式：温度(2B) + 设定温度(2B) + 模式(1B) + 星期(1B) + 周(1B) + 时(1B) + 分(1B) +
    //      开关(1B) + 高风(1B) + 中风(1B) + 低风(1B) + 冷阀(1B) + 热阀(1B) + 湿度(2B) +
    //      风速自动标志(1B) + GID(1B) + OID(1B) + IID(1B) + LOCK(1B) + 优先级字段(3B)

    int offset = 0;

    // 解析温度 (2B) - 大端序，单位0.1度
    uint16_t temperature = (data[offset] << 8) | data[offset + 1];
    offset += 2;

    // 解析设定温度 (2B) - 大端序，单位0.1度
    uint16_t set_temperature = (data[offset] << 8) | data[offset + 1];
    offset += 2;

    // 解析模式 (1B)
    uint8_t mode = data[offset++];

    // 跳过星期、周、时、分字段
    offset += 4;

    // 解析开关 (1B)
    uint8_t power_switch = data[offset++];

    // 解析风速信息
    uint8_t high_wind = data[offset++];
    uint8_t medium_wind = data[offset++];
    uint8_t low_wind = data[offset++];

    // 跳过冷阀、热阀
    offset += 2;

    // 解析湿度 (2B) - 大端序，单位0.1%
    uint16_t humidity = (data[offset] << 8) | data[offset + 1];
    offset += 2;

    // 解析风速自动标志
    uint8_t wind_auto_flag = data[offset++];

    nlog_debug("FCM parsed data - temp:%d.%d°C, set_temp:%d.%d°C, mode:%d, switch:%d, humidity:%d.%d%%",
               temperature/10, temperature%10, set_temperature/10, set_temperature%10,
               mode, power_switch, humidity/10, humidity%10);

    // 更新对应的点位值
    ret = fcm_update_tag_values(plugin, dev, temperature, set_temperature, mode,
                               power_switch, high_wind, medium_wind, low_wind,
                               humidity, wind_auto_flag);

    return ret;
}

/*
* 处理 FCM 设备点值上报数据
*/
static int fcm_process_point_value_data(neu_plugin_t *plugin, node_base_t * dev, neu_acme_dev_data_pop_t *pInfo)
{
    int ret = 0;
    // TODO: 实现点值上报数据处理
    nlog_debug("FCM point value data processing not implemented yet");
    return ret;
}

/*
* 更新 FCM 设备的点位值
* 通过遍历 adapter 中的 groups 和 tags，使用 address 字段匹配来更新对应的点位
*/
static int fcm_update_tag_values(neu_plugin_t *plugin, node_base_t * dev,
                                uint16_t temperature, uint16_t set_temperature, uint8_t mode,
                                uint8_t power_switch, uint8_t high_wind, uint8_t medium_wind,
                                uint8_t low_wind, uint16_t humidity, uint8_t wind_auto_flag)
{
    int ret = 0;

    // 获取 adapter
    neu_adapter_t *adapter = plugin->common.adapter;
    if(adapter == NULL) {
        nlog_debug("Failed to get adapter");
        return -1;
    }

    // 获取 driver
    neu_adapter_driver_t *driver = (neu_adapter_driver_t *)adapter;
    if(driver == NULL) {
        nlog_debug("Failed to get driver");
        return -1;
    }

    // 计算风速等级
    int32_t wind_speed = 0;  // 0-停止
    if(wind_auto_flag) {
        wind_speed = 4;  // 自动
    } else if(high_wind) {
        wind_speed = 1;  // 高速
    } else if(medium_wind) {
        wind_speed = 2;  // 中速
    } else if(low_wind) {
        wind_speed = 3;  // 低速
    }

    nlog_debug("FCM updating tag values - temp:%.1f, set_temp:%.1f, mode:%d, switch:%d, wind:%d, humidity:%d",
               (float)temperature/10.0, (float)set_temperature/10.0, mode, power_switch, wind_speed, humidity/10);

    // 更新各个点位
    ret += fcm_update_tag_by_address(plugin, driver, "1", NEU_TYPE_INT32, fcm_create_int_value((int32_t)power_switch));    // ONOFF
    ret += fcm_update_tag_by_address(plugin, driver, "2", NEU_TYPE_FLOAT, fcm_create_float_value((float)set_temperature/10.0)); // STEMP
    ret += fcm_update_tag_by_address(plugin, driver, "3", NEU_TYPE_INT32, fcm_create_int_value((int32_t)mode));            // SMODE
    ret += fcm_update_tag_by_address(plugin, driver, "4", NEU_TYPE_INT32, fcm_create_int_value(wind_speed));               // WSPED
    ret += fcm_update_tag_by_address(plugin, driver, "5", NEU_TYPE_FLOAT, fcm_create_float_value((float)temperature/10.0));  // RTEMP
    ret += fcm_update_tag_by_address(plugin, driver, "7", NEU_TYPE_INT32, fcm_create_int_value((int32_t)humidity/10));     // HUMID

    return ret;
}

/*
* 通过 address 字段匹配并更新点位值
* 遍历 driver 中的所有 groups 和 tags，找到匹配的 address 并更新值
*/
static int fcm_update_tag_by_address(neu_plugin_t *plugin, neu_adapter_driver_t *driver,
                                     const char *address, neu_type_e tag_type, neu_value_u value)
{
    int ret = 0;
    bool tag_found = false;

    if(plugin == NULL || driver == NULL || address == NULL) {
        nlog_debug("Invalid parameters for tag update");
        return -1;
    }

    // 获取所有 groups
    UT_array *groups = neu_adapter_driver_get_group(driver);
    if(groups == NULL) {
        nlog_debug("Failed to get groups from driver");
        return -1;
    }

    // 遍历所有 groups
    utarray_foreach(groups, neu_resp_group_info_t *, group_info)
    {
        UT_array *tags = NULL;

        // 获取该 group 中的所有 tags
        if(neu_adapter_driver_get_tag(driver, group_info->name, &tags) != 0) {
            nlog_debug("Failed to get tags from group: %s", group_info->name);
            continue;
        }

        if(tags == NULL) {
            continue;
        }

        // 遍历该 group 中的所有 tags
        utarray_foreach(tags, neu_datatag_t *, tag)
        {
            // 比较 address 字段
            if(tag->address != NULL && strcmp(tag->address, address) == 0) {
                nlog_debug("Found matching tag: %s (address: %s) in group: %s",
                          tag->name, tag->address, group_info->name);

                // 更新该点位的值
                ret = fcm_update_tag_value_direct(plugin, driver, group_info->name,
                                                 tag->name, tag_type, value);
                if(ret == 0) {
                    nlog_debug("Successfully updated tag: %s with address: %s",
                              tag->name, address);
                    tag_found = true;
                } else {
                    nlog_debug("Failed to update tag: %s with address: %s",
                              tag->name, address);
                }
                break; // 找到匹配的 tag 后退出内层循环
            }
        }

        utarray_free(tags);

        if(tag_found) {
            break; // 找到并更新了 tag 后退出外层循环
        }
    }

    utarray_free(groups);

    if(!tag_found) {
        nlog_debug("No tag found with address: %s", address);
        ret = -1;
    }

    return ret;
}

/*
* 直接更新指定 group 和 tag 的值
* 使用 driver cache 机制来更新点位值
*/
static int fcm_update_tag_value_direct(neu_plugin_t *plugin, neu_adapter_driver_t *driver,
                                       const char *group_name, const char *tag_name,
                                       neu_type_e tag_type, neu_value_u value)
{
    int ret = 0;

    if(plugin == NULL || driver == NULL || group_name == NULL || tag_name == NULL) {
        nlog_debug("Invalid parameters for direct tag update");
        return -1;
    }

    // 构造 neu_dvalue_t 结构
    neu_dvalue_t dvalue = {0};
    dvalue.type = tag_type;

    switch(tag_type) {
        case NEU_TYPE_FLOAT:
            dvalue.value.f32 = value.f32;
            break;
        case NEU_TYPE_INT32:
            dvalue.value.i32 = value.i32;
            break;
        case NEU_TYPE_UINT32:
            dvalue.value.u32 = value.u32;
            break;
        default:
            nlog_debug("Unsupported tag type: %d for tag: %s", tag_type, tag_name);
            return -1;
    }

    // 使用 adapter 的 update 回调函数来更新点位值
    if(driver->adapter.cb_funs.driver.update != NULL) {
        driver->adapter.cb_funs.driver.update((neu_adapter_t *)driver, group_name, tag_name, dvalue);
        nlog_debug("Updated tag: %s in group: %s with value type: %d", tag_name, group_name, tag_type);
        ret = 0;
    } else {
        nlog_debug("Update callback not available for driver");
        ret = -1;
    }

    return ret;
}

/*
* 辅助函数：创建浮点型 neu_value_u
*/
static neu_value_u fcm_create_float_value(float float_value)
{
    neu_value_u value;
    value.f32 = float_value;
    return value;
}

/*
* 辅助函数：创建整型 neu_value_u
*/
static neu_value_u fcm_create_int_value(int32_t int_value)
{
    neu_value_u value;
    value.i32 = int_value;
    return value;
}

