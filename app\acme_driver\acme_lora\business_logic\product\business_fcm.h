/**
 * NEURON IIoT System for Industry 4.0
 * Copyright (C) 2020-2024 EMQ Technologies Co., Ltd All rights reserved.
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 3 of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this program; if not, write to the Free Software Foundation,
 * Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.
 **/

#ifndef NEURON_PLUGIN_ACME_LORA_BUSINESS_FCM_H
#define NEURON_PLUGIN_ACME_LORA_BUSINESS_FCM_H

#ifdef __cplusplus
extern "C" {
#endif
#include <stdlib.h>
#include <neuron.h>
#include "neuron.h"
#include "acme_node.h"

#define ACME_FCM_DEFAULT_INTERVAL      5000        //FCM 设备点位默认采集周期
#define ACME_FCM_DEFUALT_TAGS_NUM       7           //FCM 默认设备的点位数

#define ACME_FCM_ADDR_ONOFF                 1
#define ACME_FCM_ADDR_STEMP                 2
#define ACME_FCM_ADDR_SMODE                 3
#define ACME_FCM_ADDR_WSPED                 4
#define ACME_FCM_ADDR_RTEMP                 5
#define ACME_FCM_ADDR_ERROC                 6
#define ACME_FCM_ADDR_HUMID                 7

#define ACME_FCM_TAG_TYPE_AO                "AO"
#define ACME_FCM_TAG_TYPE_BO                "BO"

#define ACME_FCM_CHN_ONOFF                      0       //开关 通道
#define ACME_FCM_CHN_FAN_HIGH                   1       //高速 通道
#define ACME_FCM_CHN_FAN_MEDIUM                 2       //中速 通道
#define ACME_FCM_CHN_FAN_LOW                    3       //低速 通道
#define ACME_FCM_CHN_COOL_VALVES                4       //制冷阀 通道
#define ACME_FCM_CHN_HEAT_VALVES                5       //制热阀 通道
#define ACME_FCM_CHN_FAN_AUTO                   6       //自动风 通道

#define ACME_FCM_CHN_SET_TEMP                   0       //设置温度 通道
#define ACME_FCM_CHN_MODE                       1       //模式 通道
#define ACME_FCM_CHN_WEEK                       2       //星期 通道
#define ACME_FCM_CHN_HOUR                       3       //小时 通道
#define ACME_FCM_CHN_MINUTE                     4       //分钟 通道



void * fcm_dev_search(char *node_name);
int fcm_dev_add_group(neu_plugin_t *plugin, node_base_t * dev ,neu_req_add_group_t *group);
int fcm_dev_ctrl(neu_plugin_t *plugin, node_base_t * dev ,void *params);

int fcm_dev_write_tag(neu_plugin_t *plugin, node_base_t * dev,neu_datatag_t *tag,neu_value_u value);

#ifdef __cplusplus
}
#endif

#endif
