/**
 * NEURON IIoT System for Industry 4.0
 * Copyright (C) 2020-2022 EMQ Technologies Co., Ltd All rights reserved.
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 3 of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this program; if not, write to the Free Software Foundation,
 * Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.
 **/

/*
 * DO NOT EDIT THIS FILE MANUALLY!
 * It was automatically generated by `neuron-version.cmake`
 */

#ifndef NEURON_VERSION_H
#define NEURON_VERSION_H

#define NEURON_VERSION "2.11.2"

#define NEURON_VERSION_MAJOR 2
#define NEURON_VERSION_MINOR 11
#define NEURON_VERSION_MICRO 2

#define NEURON_GIT_REV "bf07aef"
#define NEURON_GIT_TAG ""
#define NEURON_GIT_DIFF ""
#define NEURON_GIT_BRANCH "master"

#define NEURON_BUILD_DATE "2025-08-15"

#endif
